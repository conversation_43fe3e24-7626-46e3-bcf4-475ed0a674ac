// 临时菜单刷新工具
// 在浏览器控制台中运行这段代码来强制刷新菜单

console.log('🔄 开始清除菜单缓存...')

// 清除localStorage中的菜单缓存
try {
  localStorage.removeItem('user_menu_data')
  localStorage.removeItem('user_menu_timestamp')
  localStorage.removeItem('currentMenu')
  console.log('✅ localStorage菜单缓存已清除')
} catch (error) {
  console.error('❌ 清除localStorage失败:', error)
}

// 清除sessionStorage中的菜单缓存
try {
  sessionStorage.removeItem('user_menu_data')
  sessionStorage.removeItem('user_menu_timestamp')
  console.log('✅ sessionStorage菜单缓存已清除')
} catch (error) {
  console.error('❌ 清除sessionStorage失败:', error)
}

// 如果在Vue应用中，可以通过store强制刷新菜单
if (window.$store) {
  try {
    window.$store.dispatch('menu/refreshUserMenus')
    console.log('✅ 已通过store刷新菜单')
  } catch (error) {
    console.error('❌ 通过store刷新菜单失败:', error)
  }
}

console.log('🎯 菜单缓存清除完成，请刷新页面查看效果')

// 自动刷新页面
setTimeout(() => {
  window.location.reload()
}, 1000)