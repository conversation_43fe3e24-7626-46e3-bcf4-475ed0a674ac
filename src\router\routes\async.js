/**
 * 异步路由配置
 * 需要权限验证的动态路由

 */

import LayoutContainer from '@/components/layout/LayoutContainer.vue'

export const asyncRoutes = [
  // 服务项目模块路由
  {
    path: '/service',
    name: 'Service',
    component: LayoutContainer,
    redirect: '/service/list',
    meta: {
      title: '服务管理',
      icon: 'Service',
      menuName: 'Service',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'list',
        name: 'ServiceList',
        component: () => import('@/views/service/ServiceList.vue'),
        meta: {
          title: '服务项目',
          keepAlive: true
        }
      },
      {
        path: 'edit',
        name: 'ServiceEdit',
        component: () => import('@/views/service/ServiceEdit.vue'),
        meta: {
          title: '编辑服务项目',
          hidden: true
        }
      },
      {
        path: 'banner',
        name: 'ServiceBanner',
        component: () => import('@/views/service/ServiceBanner.vue'),
        meta: {
          title: '轮播图设置'
        }
      },
      {
        path: 'jingang',
        name: 'ServiceJingang',
        component: () => import('@/views/service/ServiceJingang.vue'),
        meta: {
          title: '金刚区设置'
        }
      },
      {
        path: 'fenlei',
        name: 'ServiceFenlei',
        component: () => import('@/views/service/ServiceFenlei.vue'),
        meta: {
          title: '分类设置'
        }
      },
      {
        path: 'daili',
        name: 'ServiceDaili',
        component: () => import('@/views/service/ServiceDaili.vue'),
        meta: {
          title: '服务点设置'
        }
      },
      {
        path: 'peizhi',
        name: 'ServicePeizhi',
        component: () => import('@/views/service/ServicePeizhi.vue'),
        meta: {
          title: '项目配置'
        }
      },
    {
      path: 'text',
      name: 'ServiceText',
      component: () => import('@/views/service/ServiceText.vue'),
      meta: { title: '测试项目' }
    }
    ]
  },

  // 师傅管理模块路由
  {
    path: '/technician',
    name: 'Technician',
    component: LayoutContainer,
    redirect: '/technician/list',
    meta: {
      title: '师傅管理',
      icon: 'User',
      menuName: 'Technician',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'list',
        name: 'TechnicianList',
        component: () => import('@/views/technician/TechnicianList.vue'),
        meta: {
          title: '师傅管理'
        }
      },
      {
        path: 'edit',
        name: 'TechnicianEdit',
        component: () => import('@/views/technician/TechnicianEdit.vue'),
        meta: {
          title: '新增师傅'
        }
      },
      {
        path: 'level',
        name: 'TechnicianLevel',
        component: () => import('@/views/technician/TechnicianLevel.vue'),
        meta: {
          title: '师傅等级'
        }
      },
      {
        path: 'deposit',
        name: 'TechnicianDeposit',
        component: () => import('@/views/technician/TechnicianDeposit.vue'),
        meta: {
          title: '师傅押金'
        }
      },
      {
        path: 'distance',
        name: 'TechnicianDistance',
        component: () => import('@/views/technician/TechnicianDistance.vue'),
        meta: {
          title: '接单范围'
        }
      },
      {
        path: 'city',
        name: 'TechnicianCity',
        component: () => import('@/views/technician/TechnicianCity.vue'),
        meta: {
          title: '城市管理'
        }
      },
      {
        path: 'blacklist',
        name: 'TechnicianBlacklist',
        component: () => import('@/views/technician/BlackList.vue'),
        meta: {
          title: '黑名单管理'
        }
      },
      {
        path: 'log',
        name: 'TechnicianLog',
        component: () => import('@/views/technician/TechnicianLog.vue'),
        meta: {
          title: '日志管理'
        }
      },
      {
        path: 'margin-refund',
        name: 'MarginRefund',
        component: () => import('@/views/technician/MarginRefund.vue'),
        meta: {
          title: '保证金退款管理'
        }
      }
    ]
  },

  // 营销管理模块路由
  {
    path: '/market',
    name: 'Market',
    component: LayoutContainer,
    redirect: '/market/list',
    meta: {
      title: '营销管理',
      icon: 'Promotion',
      menuName: 'Market',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'list',
        name: 'MarketList',
        component: () => import('@/views/market/MarketList.vue'),
        meta: {
          title: '卡券管理'
        }
      },  {
        path: 'edit',
        name: 'MarketEdit',
        component: () => import('@/views/market/MarketEdit.vue'),
        meta: {
          title: '编辑卡券',
          hidden: true
        }
      },
      {
        path: 'notice',
        name: 'MarketNotice',
        component: () => import('@/views/market/MarketNotice.vue'),
        meta: {
          title: '公告设置'
        }
      },
      {
        path: 'partner',
        name: 'MarketPartner',
        component: () => import('@/views/market/MarketPartner.vue'),
        meta: {
          title: '合伙人管理'
        }
      },
      {
        path: 'partner/invite',
        name: 'MarketPartnerInvite',
        component: () => import('@/views/market/MarketPartnerInvite.vue'),
        meta: {
          title: '合伙人邀请列表'
        }
      },
      {
        path: 'partner/commission',
        name: 'MarketPartnerCommission',
        component: () => import('@/views/market/MarketPartnerCommission.vue'),
        meta: {
          title: '合伙人佣金统计'
        }
      },
      {
        path: 'partner/orders',
        name: 'MarketPartnerOrders',
        component: () => import('@/views/market/MarketPartnerOrders.vue'),
        meta: {
          title: '合伙人推广订单'
        }
      }
    ]
  },

  // 订单管理模块路由
  {
    path: '/shop',
    name: 'Shop',
    component: LayoutContainer,
    redirect: '/shop/order',
    meta: {
      title: '订单管理',
      icon: 'Document',
      menuName: 'Shop',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'order',
        name: 'ShopOrder',
        component: () => import('@/views/shop/ShopOrder.vue'),
        meta: {
          title: '订单管理',
          keepAlive: true
        }
      },
      {
        path: 'order/detail',
        name: 'ShopOrderDetail',
        component: () => import('@/views/shop/ShopOrderDetail.vue'),
        meta: {
          title: '订单详情',
          hidden: true
        }
      },
      {
        path: 'refund',
        name: 'ShopRefund',
        component: () => import('@/views/shop/ShopRefund.vue'),
        meta: {
          title: '退款管理'
        }
      },
      {
        path: 'evaluate',
        name: 'ShopEvaluate',
        component: () => import('@/views/shop/ShopEvaluate.vue'),
        meta: {
          title: '评价管理'
        }
      },
      {
        path: 'commission',
        name: 'ShopCommission',
        component: () => import('@/views/shop/ShopCommission.vue'),
        meta: {
          title: '分销佣金'
        }
      },
      {
        path: 'aftersale',
        name: 'ShopAfterSale',
        component: () => import('@/views/shop/ShopAfterSale.vue'),
        meta: {
          title: '售后管理'
        }
      }
    ]
  },

  // 用户管理模块路由
  {
    path: '/user',
    name: 'User',
    component: LayoutContainer,
    redirect: '/user/list',
    meta: {
      title: '用户管理',
      icon: 'User',
      menuName: 'User',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户管理',
          keepAlive: true
        }
      },
      {
        path: 'log',
        name: 'UserLog',
        component: () => import('@/views/user/UserLog.vue'),
        meta: {
          title: '操作日志'
        }
      }
    ]
  },

  // 用户中心
  {
    path: '/user-center',
    component: LayoutContainer,
    redirect: '/user-center/profile',
    meta: {
      title: '用户中心',
      icon: 'User'
    },
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/ProfileView.vue'),
        meta: {
          title: '个人资料',
          icon: 'User'
        }
      },
      {
        path: 'settings',
        name: 'UserSettings',
        component: () => import('@/views/user/SettingsView.vue'),
        meta: {
          title: '账户设置',
          icon: 'Setting'
        }
      },
      {
        path: 'security',
        name: 'UserSecurity',
        component: () => import('@/views/user/SecurityView.vue'),
        meta: {
          title: '安全设置',
          icon: 'Lock'
        }
      }
    ]
  },

  // 数据统计
  {
    path: '/statistics',
    component: LayoutContainer,
    redirect: '/statistics/overview',
    meta: {
      title: '数据统计',
      icon: 'DataAnalysis',
      roles: ['admin', 'super_admin']
    },
    children: [
      {
        path: 'overview',
        name: 'StatisticsOverview',
        component: () => import('@/views/statistics/OverviewView.vue'),
        meta: {
          title: '数据概览',
          icon: 'Odometer',
          roles: ['admin', 'super_admin']
        }
      },
      {
        path: 'user-analysis',
        name: 'StatisticsUserAnalysis',
        component: () => import('@/views/statistics/UserAnalysisView.vue'),
        meta: {
          title: '用户分析',
          icon: 'TrendCharts',
          roles: ['admin', 'super_admin']
        }
      },
      {
        path: 'content-analysis',
        name: 'StatisticsContentAnalysis',
        component: () => import('@/views/statistics/ContentAnalysisView.vue'),
        meta: {
          title: '内容分析',
          icon: 'PieChart',
          roles: ['admin', 'super_admin']
        }
      }
    ]
  },

  // 工具箱
  {
    path: '/tools',
    component: LayoutContainer,
    redirect: '/tools/generator',
    meta: {
      title: '工具箱',
      icon: 'Tools',
      roles: ['admin', 'super_admin']
    },
    children: [
      {
        path: 'generator',
        name: 'ToolsGenerator',
        component: () => import('@/views/tools/GeneratorView.vue'),
        meta: {
          title: '代码生成',
          icon: 'DocumentAdd',
          roles: ['admin', 'super_admin']
        }
      },
      {
        path: 'backup',
        name: 'ToolsBackup',
        component: () => import('@/views/tools/BackupView.vue'),
        meta: {
          title: '数据备份',
          icon: 'FolderAdd',
          roles: ['super_admin']
        }
      },
      {
        path: 'monitor',
        name: 'ToolsMonitor',
        component: () => import('@/views/tools/MonitorView.vue'),
        meta: {
          title: '系统监控',
          icon: 'Monitor',
          roles: ['super_admin']
        }
      }
    ]
  },

  // 日志管理模块路由
  {
    path: '/log',
    name: 'Log',
    component: LayoutContainer,
    redirect: '/log/operation',
    meta: {
      title: '日志管理',
      icon: 'Document',
      menuName: 'Log',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'operation',
        name: 'LogOperation',
        component: () => import('@/views/log/OperationLog.vue'),
        meta: {
          title: '操作日志',
          keepAlive: true
        }
      }
    ]
  },

  // 外部链接示例
  {
    path: '/external-link',
    component: LayoutContainer,
    meta: {
      title: '外部链接',
      icon: 'Link'
    },
    children: [
      {
        path: 'vue-docs',
        name: 'VueDocs',
        meta: {
          title: 'Vue文档',
          icon: 'Document',
          isExternal: true,
          url: 'https://vuejs.org/'
        }
      },
      {
        path: 'element-plus-docs',
        name: 'ElementPlusDocs',
        meta: {
          title: 'Element Plus文档',
          icon: 'Document',
          isExternal: true,
          url: 'https://element-plus.org/'
        }
      }
    ]
  }
]
