/**
 * 动态菜单管理模块
 * 根据登录接口返回的菜单数据动态生成侧边栏菜单
 */

const state = {
  // 原始菜单数据（从接口获取）
  rawMenuData: [],
  // 处理后的主菜单列表
  mainMenuList: [],
  // 子菜单映射
  submenuMap: {},
  // 菜单是否已加载
  menuLoaded: false
}

const mutations = {
  // 设置原始菜单数据
  SET_RAW_MENU_DATA(state, data) {
    state.rawMenuData = data
  },

  // 设置主菜单列表
  SET_MAIN_MENU_LIST(state, menuList) {
    state.mainMenuList = menuList
  },

  // 设置子菜单映射
  SET_SUBMENU_MAP(state, submenuMap) {
    state.submenuMap = submenuMap
  },

  // 设置菜单加载状态
  SET_MENU_LOADED(state, loaded) {
    state.menuLoaded = loaded
  },

  // 重置菜单状态
  RESET_MENU_STATE(state) {
    state.rawMenuData = []
    state.mainMenuList = []
    state.submenuMap = {}
    state.menuLoaded = false
  }
}

const actions = {
  /**
   * 直接设置菜单数据（用于登录时直接获取的菜单数据）
   * @param {Array} menuData 菜单数据数组
   */
  async setMenuData({ commit, state }, menuData) {
    console.log('🌲 直接设置菜单数据:', menuData)

    if (!menuData || !Array.isArray(menuData)) {
      console.warn('⚠️ 菜单数据格式无效，使用降级菜单')
      const fallbackMenus = getFallbackMenus()
      commit('SET_MAIN_MENU_LIST', fallbackMenus.mainMenuList)
      commit('SET_SUBMENU_MAP', fallbackMenus.submenuMap)
      commit('SET_MENU_LOADED', true)
      return fallbackMenus
    }

    // 保存原始数据
    commit('SET_RAW_MENU_DATA', menuData)

    // 处理菜单数据
    const { mainMenuList, submenuMap } = processMenuData(menuData)

    // 验证菜单数据完整性
    if (mainMenuList.length === 0) {
      console.warn('⚠️ 处理后的菜单数据为空，使用降级菜单')
      const fallbackMenus = getFallbackMenus()
      commit('SET_MAIN_MENU_LIST', fallbackMenus.mainMenuList)
      commit('SET_SUBMENU_MAP', fallbackMenus.submenuMap)
    } else {
      // 保存处理后的数据
      commit('SET_MAIN_MENU_LIST', mainMenuList)
      commit('SET_SUBMENU_MAP', submenuMap)
    }

    commit('SET_MENU_LOADED', true)
    console.log('🎯 菜单数据设置完成:', {
      主菜单数量: mainMenuList.length,
      子菜单数量: Object.keys(submenuMap).length
    })

    return { mainMenuList, submenuMap }
  },

  /**
   * 获取用户菜单数据
   * 不再调用API，优先使用缓存，否则使用降级菜单
   */
  async fetchUserMenus({ commit, state }) {
    console.log('🔄 fetchUserMenus 被调用，但不再调用API接口')

    // 如果菜单已加载，直接返回缓存数据
    if (state.menuLoaded && state.mainMenuList.length > 0) {
      console.log('✅ 使用内存缓存的菜单数据')
      return {
        mainMenuList: state.mainMenuList,
        submenuMap: state.submenuMap
      }
    }

    // 尝试从本地存储恢复菜单数据
    try {
      const cachedMenuData = localStorage.getItem('user_menu_data')
      const cacheTimestamp = localStorage.getItem('user_menu_timestamp')
      const cacheExpiry = 30 * 60 * 1000 // 30分钟缓存过期时间

      if (cachedMenuData && cacheTimestamp) {
        const isExpired = Date.now() - parseInt(cacheTimestamp) > cacheExpiry

        if (!isExpired) {
          console.log('✅ 使用本地存储的菜单数据')
          const menuData = JSON.parse(cachedMenuData)

          // 恢复菜单状态
          commit('SET_RAW_MENU_DATA', menuData)
          const { mainMenuList, submenuMap } = processMenuData(menuData)
          commit('SET_MAIN_MENU_LIST', mainMenuList)
          commit('SET_SUBMENU_MAP', submenuMap)
          commit('SET_MENU_LOADED', true)

          return { mainMenuList, submenuMap }
        } else {
          console.log('⏰ 本地缓存已过期，清除缓存')
          localStorage.removeItem('user_menu_data')
          localStorage.removeItem('user_menu_timestamp')
        }
      }
    } catch (error) {
      console.warn('⚠️ 恢复本地菜单缓存失败:', error)
      localStorage.removeItem('user_menu_data')
      localStorage.removeItem('user_menu_timestamp')
    }

    // 不再调用API，直接使用降级菜单
    console.log('🔄 不再调用API，使用降级菜单')
    const fallbackMenus = getFallbackMenus()
    commit('SET_MAIN_MENU_LIST', fallbackMenus.mainMenuList)
    commit('SET_SUBMENU_MAP', fallbackMenus.submenuMap)
    commit('SET_MENU_LOADED', true)

    console.log('🎯 降级菜单设置完成:', {
      主菜单数量: fallbackMenus.mainMenuList.length,
      子菜单数量: Object.keys(fallbackMenus.submenuMap).length
    })

    return fallbackMenus
  },

  /**
   * 使用降级菜单
   * 当登录接口没有返回菜单数据时使用
   */
  async useFallbackMenus({ commit }) {
    console.log('🔄 使用降级菜单配置...')

    const fallbackMenus = getFallbackMenus()
    commit('SET_MAIN_MENU_LIST', fallbackMenus.mainMenuList)
    commit('SET_SUBMENU_MAP', fallbackMenus.submenuMap)
    commit('SET_MENU_LOADED', true)

    console.log('✅ 降级菜单设置完成:', {
      主菜单数量: fallbackMenus.mainMenuList.length,
      子菜单数量: Object.keys(fallbackMenus.submenuMap).length
    })

    return fallbackMenus
  },

  /**
   * 强制刷新菜单数据
   * 清除缓存并使用降级菜单
   */
  async refreshUserMenus({ commit, dispatch }) {
    console.log('🔄 强制刷新菜单数据...')

    // 先清除本地缓存
    try {
      localStorage.removeItem('user_menu_data')
      localStorage.removeItem('user_menu_timestamp')
      console.log('🗑️ 已清除本地菜单缓存')
    } catch (error) {
      console.warn('⚠️ 清除本地菜单缓存失败:', error)
    }

    // 重置状态
    commit('RESET_MENU_STATE')

    // 使用降级菜单而不是调用API
    return await dispatch('useFallbackMenus')
  },

  /**
   * 重置菜单状态
   */
  resetMenus({ commit }) {
    commit('RESET_MENU_STATE')

    // 清除本地存储的菜单缓存
    try {
      localStorage.removeItem('user_menu_data')
      localStorage.removeItem('user_menu_timestamp')
      console.log('🗑️ 已清除本地菜单缓存')
    } catch (error) {
      console.warn('⚠️ 清除本地菜单缓存失败:', error)
    }
  },

  /**
   * 清除菜单缓存（手动清除）
   */
  clearMenuCache({ commit }) {
    console.log('🗑️ 手动清除菜单缓存')

    // 重置状态
    commit('RESET_MENU_STATE')

    // 清除本地存储
    try {
      localStorage.removeItem('user_menu_data')
      localStorage.removeItem('user_menu_timestamp')
      console.log('✅ 菜单缓存清除完成')
    } catch (error) {
      console.warn('⚠️ 清除本地菜单缓存失败:', error)
    }
  }
}

const getters = {
  // 获取主菜单列表
  mainMenuList: state => state.mainMenuList,

  // 获取子菜单映射
  submenuMap: state => state.submenuMap,

  // 获取菜单加载状态
  menuLoaded: state => state.menuLoaded,

  // 根据路径获取子菜单
  getSubmenuByPath: state => path => {
    return state.submenuMap[path] || []
  },

  // 检查是否有子菜单
  hasSubmenu: state => path => {
    const submenu = state.submenuMap[path]
    return submenu && submenu.length > 0
  },

  // 获取默认路由
  getDefaultRoute: state => path => {
    const submenu = state.submenuMap[path]
    if (submenu && submenu.length > 0 && submenu[0].url && submenu[0].url.length > 0) {
      return submenu[0].url[0].url
    }
    return path
  },

  // 根据当前路由获取主菜单路径
  getMainMenuByRoute: state => currentRoute => {
    for (const menu of state.mainMenuList) {
      if (currentRoute.startsWith(menu.path)) {
        return menu.path
      }
    }
    return state.mainMenuList.length > 0 ? state.mainMenuList[0].path : '/service'
  }
}

/**
 * 处理菜单数据
 * 将接口返回的菜单数据转换为侧边栏需要的格式，支持动态子菜单
 */
function processMenuData(menuData) {
  console.log('🔄 开始处理菜单数据:', menuData)

  // 按sort字段排序
  const sortedMenus = [...menuData].sort((a, b) => a.sort - b.sort)

  // 分离父菜单和子菜单
  const parentMenus = sortedMenus.filter(menu =>
    menu.isShow === 1 && menu.isMenu === 1 && (!menu.parentId || menu.parentId === 0)
  )
  const childMenus = sortedMenus.filter(menu =>
    menu.isShow === 1 && menu.isMenu === 1 && menu.parentId && menu.parentId !== 0
  )

  console.log('📊 菜单分类:', {
    父菜单数量: parentMenus.length,
    子菜单数量: childMenus.length,
    父菜单: parentMenus.map(m => m.menuName),
    子菜单: childMenus.map(m => `${m.menuName}(父ID:${m.parentId})`)
  })

  // 生成主菜单列表
  const mainMenuList = parentMenus.map(menu => ({
    name: menu.menuName,
    path: menu.route,
    icon: getMenuIcon(menu.menuName), // 根据菜单名称映射图标
    menuName: menu.menuName,
    id: menu.id,
    sort: menu.sort
  }))

  // 生成子菜单映射，优先使用后端返回的子菜单数据
  const submenuMap = {}

  parentMenus.forEach(parentMenu => {
    // 查找该父菜单下的所有子菜单
    const children = childMenus
      .filter(child => child.parentId === parentMenu.id)
      .sort((a, b) => a.sort - b.sort)

    if (children.length > 0) {
      // 如果后端返回了子菜单数据，使用后端数据
      console.log(`🌿 使用后端子菜单数据 ${parentMenu.menuName}:`, children)
      submenuMap[parentMenu.route] = [
        {
          name: parentMenu.menuName,
          url: children.map(child => ({
            name: child.menuName,
            url: child.route,
            id: child.id,
            sort: child.sort
          }))
        }
      ]
    } else {
      // 如果后端没有返回子菜单，使用前端配置的子菜单
      console.log(`🌿 使用前端配置子菜单 ${parentMenu.menuName}`)
      const subMenus = getSubMenusByRoute(parentMenu.route, parentMenu.menuName)
      submenuMap[parentMenu.route] = subMenus
    }
  })

  console.log('🎯 菜单处理结果:', {
    主菜单: mainMenuList,
    子菜单映射: submenuMap
  })

  return { mainMenuList, submenuMap }
}

/**
 * 根据菜单名称映射图标
 */
function getMenuIcon(menuName) {
  const iconMap = {
    '服务管理': 'Service',
    '师傅管理': 'User',
    '营销管理': 'Promotion',
    '订单管理': 'Document',
    '分销管理': 'Share',
    '财务管理': 'Money',
    '用户管理': 'UserFilled',
    '账号设置': 'Setting',
    '系统设置': 'Tools',
    '日志管理': 'Document'
  }

  return iconMap[menuName] || 'Menu'
}

/**
 * 根据主菜单路由获取对应的子菜单列表
 * 这里定义了每个模块下的子菜单结构
 */
function getSubMenusByRoute(route, menuName) {
  const subMenusConfig = {
    '/service': [
      {
        name: '服务管理',
        url: [
          { name: '服务项目', url: '/service/list' },
          { name: '轮播图设置', url: '/service/banner' },
          { name: '金刚区设置', url: '/service/jingang' },
          { name: '分类设置', url: '/service/fenlei' },
          { name: '服务点设置', url: '/service/daili' },
          { name: '项目配置', url: '/service/peizhi' }
        ]
      }
    ],
    '/technician': [
      {
        name: '师傅管理',
        url: [
          { name: '师傅列表', url: '/technician/list' },
          { name: '师傅等级', url: '/technician/level' },
          { name: '师傅押金', url: '/technician/deposit' },
          { name: '保证金退款管理', url: '/technician/margin-refund' },
          { name: '城市管理', url: '/technician/city' },
          { name: '黑名单管理', url: '/technician/blacklist' },
          { name: '日志管理', url: '/technician/log' },
          { name: '师傅信誉分变更日志', url: '/technician/credit-log' }
        ]
      }
    ],
    '/market': [
      {
        name: '营销管理',
        url: [
          { name: '卡券管理', url: '/market/list' },

          { name: '公告设置', url: '/market/notice' },
          { name: '合伙人管理', url: '/market/partner' }
        ]
      }
    ],
    '/shop': [
      {
        name: '订单管理',
        url: [
          { name: '订单列表', url: '/shop/order' },
          { name: '退款管理', url: '/shop/refund' },
          { name: '评价管理', url: '/shop/evaluate' },
          { name: '分销佣金', url: '/shop/commission' },
          // { name: '售后管理', url: '/shop/aftersale' }
        ]
      }
    ],
    '/distribution': [
      {
        name: '分销管理',
        url: [
          { name: '分销审核', url: '/distribution/examine' },
          { name: '分销商列表', url: '/distribution/list' },
          { name: '分销设置', url: '/distribution/config' }
        ]
      }
    ],
    '/finance': [
      {
        name: '财务管理',
        url: [
          { name: '财务列表', url: '/finance/list' },
          { name: '提现管理', url: '/finance/withdraw' },
          // { name: '储值管理', url: '/finance/stored' },
          { name: '历史提现', url: '/finance/withdraw-history' }
        ]
      }
    ],
    '/user': [
      {
        name: '用户管理',
        url: [
          { name: '用户列表', url: '/user/list' },
          { name: '操作日志', url: '/user/log' }
        ]
      }
    ],
    '/account': [
      {
        name: '账号设置',
        url: [
          { name: '管理员管理', url: '/account/admin' },
          { name: '角色管理', url: '/account/role' },
          // { name: '菜单管理', url: '/account/menu' },
          { name: '代理商管理', url: '/account/franchisee' },
          // { name: '第三方管理', url: '/account/third' }
        ]
      }
    ],
    '/sys': [
      {
        name: '版本管理',
        url: [
          { name: 'APP版本管理', url: '/sys/app-version' }
        ]
      },
      {
        name: '系统设置',
        url: [
          // { name: '系统升级', url: '/sys/upgrade' },
          // { name: '上传微信审核', url: '/sys/examine' },
          // { name: '小程序设置', url: '/sys/wechat' },
          // { name: '公众号设置', url: '/sys/web' },
          // { name: 'APP设置', url: '/sys/app' },
          { name: '隐私协议', url: '/sys/info' },
          // { name: '支付配置', url: '/sys/payment' },
          // { name: '上传配置', url: '/sys/upload' },
          { name: '交易设置', url: '/sys/transaction' },
          // { name: '万能通知', url: '/sys/notice' },
          // { name: '短信通知', url: '/sys/message' },
          // { name: '备案信息', url: '/sys/information' },
          // { name: '打印机设置', url: '/sys/print' },
          // { name: '车费设置', url: '/sys/car_fee' },
          // { name: '城市设置', url: '/sys/city' },
          // { name: '出行设置', url: '/sys/travel' },
          // { name: '其他设置', url: '/sys/other' },
          // { name: '版本管理', url: '/sys/version' }
        ]
      }
    ],
    '/log': [
      {
        name: '日志管理',
        url: [
          { name: '操作日志', url: '/log/operation' }
        ]
      }
    ]
  }

  // 返回对应路由的子菜单，如果没有配置则返回默认结构
  const submenu = subMenusConfig[route]

  // 如果找到了配置且有有效的子菜单项，返回配置
  if (submenu && submenu.length > 0) {
    // 过滤掉空的url数组
    const validSubmenu = submenu.map(group => ({
      ...group,
      url: group.url.filter(item => item && item.url) // 确保url项有效
    })).filter(group => group.url.length > 0) // 过滤掉没有有效url的分组

    if (validSubmenu.length > 0) {
      return validSubmenu
    }
  }

  // 如果没有配置或配置无效，返回默认结构
  return [
    {
      name: menuName,
      url: [
        { name: menuName, url: `${route}/list` }
      ]
    }
  ]
}

/**
 * 获取降级菜单（当接口失败时使用）
 */
function getFallbackMenus() {
  console.log('🔄 使用降级菜单配置')

  const fallbackMainMenus = [
    { name: '服务管理', path: '/service', icon: 'Service', menuName: 'Service', id: 4, sort: 1 },
    { name: '师傅管理', path: '/technician', icon: 'User', menuName: 'Technician', id: 7, sort: 2 },
    { name: '营销管理', path: '/market', icon: 'Promotion', menuName: 'Market', id: 8, sort: 3 },
    { name: '订单管理', path: '/shop', icon: 'Document', menuName: 'Shop', id: 9, sort: 4 },
    { name: '分销管理', path: '/distribution', icon: 'Share', menuName: 'Distribution', id: 10, sort: 5 },
    { name: '财务管理', path: '/finance', icon: 'Money', menuName: 'Finance', id: 11, sort: 6 },
    { name: '用户管理', path: '/user', icon: 'UserFilled', menuName: 'User', id: 12, sort: 7 },
    { name: '账号设置', path: '/account', icon: 'Setting', menuName: 'Account', id: 15, sort: 8 },
    { name: '系统设置', path: '/sys', icon: 'Tools', menuName: 'System', id: 16, sort: 9 },
    { name: '日志管理', path: '/log', icon: 'Document', menuName: 'Log', id: 17, sort: 10 }
  ]

  const fallbackSubmenuMap = {}

  // 为每个降级主菜单生成对应的子菜单
  fallbackMainMenus.forEach(menu => {
    const submenus = getSubMenusByRoute(menu.path, menu.name)
    if (submenus && submenus.length > 0) {
      fallbackSubmenuMap[menu.path] = submenus
    }
  })

  console.log('✅ 降级菜单生成完成:', {
    主菜单数量: fallbackMainMenus.length,
    子菜单数量: Object.keys(fallbackSubmenuMap).length
  })

  return {
    mainMenuList: fallbackMainMenus,
    submenuMap: fallbackSubmenuMap
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
