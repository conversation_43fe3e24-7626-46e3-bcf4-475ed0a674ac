/**
 * 侧边栏菜单配置

 * 适配 Vue3 + Element Plus + LayoutSidebar.vue 组件
 */

// 主菜单列表配置
export const mainMenuList = [
  { 
    name: '服务管理', 
    path: '/service', 
    icon: 'Service',
    menuName: 'Service'
  },
  { 
    name: '师傅管理', 
    path: '/technician', 
    icon: 'User',
    menuName: 'Technician'
  },
  { 
    name: '营销管理', 
    path: '/market', 
    icon: 'Promotion',
    menuName: 'Market'
  },
  { 
    name: '订单管理', 
    path: '/shop', 
    icon: 'Document',
    menuName: 'Shop'
  },
  { 
    name: '分销管理', 
    path: '/distribution', 
    icon: 'Share',
    menuName: 'Distribution'
  },
  { 
    name: '财务管理', 
    path: '/finance', 
    icon: 'Money',
    menuName: 'Finance'
  },
  {
    name: '用户管理',
    path: '/user',
    icon: 'UserFilled',
    menuName: 'User'
  },
  { 
    name: '账号设置', 
    path: '/account', 
    icon: 'Setting',
    menuName: 'Account'
  },
  {
    name: '系统设置',
    path: '/sys',
    icon: 'Tools',
    menuName: 'System'
  },
  {
    name: '日志管理',
    path: '/log',
    icon: 'Document',
    menuName: 'Log'
  }
]

// 子菜单配置映射
export const submenuMap = {
  '/service': [
    {
      name: '服务项目',
      url: [{ name: '服务项目', url: '/service/list' }]
    },
    {
      name: '项目设置', 
      url: [{ name: '轮播图设置', url: '/service/banner' }]
    },
    {
      name: '金刚区设置',
      url: [{ name: '金刚区设置', url: '/service/jingang' }]
    },
    {
      name: '分类设置',
      url: [{ name: '分类设置', url: '/service/fenlei' }]
    },
    {
      name: '服务点设置',
      url: [{ name: '服务点设置', url: '/service/daili' }]
    },
    {
      name: '项目配置',
      url: [{ name: '项目配置', url: '/service/peizhi' }]
    },
    {
      name: '测试项目',
      url: [{ name: '测试项目', url: '/service/text' }]
    }
  ],

  '/technician': [
    {
      name: '师傅管理',
      url: [
        { name: '师傅管理', url: '/technician/list' },
        { name: '师傅等级', url: '/technician/level' },
        { name: '师傅押金', url: '/technician/deposit' },
        { name: '保证金退款管理', url: '/technician/margin-refund' },
        { name: '城市管理', url: '/technician/city' },
        { name: '日志管理', url: '/technician/log' }
      ]
    }
  ],

  '/market': [
    {
      name: '营销管理',
      url: [{ name: '卡券管理', url: '/market/list' }]
    },
    {
      name: '公告设置',
      url: [{ name: '公告设置', url: '/market/notice' }]
    },
    {
      name: '合伙人管理',
      url: [
        { name: '合伙人管理', url: '/market/partner' },
        // { name: '合伙人邀请列表', url: '/market/partner/invite' },
        // { name: '合伙人佣金统计', url: '/market/partner/commission' },
        // { name: '合伙人推广订单', url: '/market/partner/orders' }
      ]
    }
  ],

  '/shop': [
    {
      name: '订单管理',
      url: [
        { name: '订单管理', url: '/shop/order' },
        { name: '退款管理', url: '/shop/refund' }
      ]
    },
    {
      name: '评价管理',
      url: [{ name: '评价管理', url: '/shop/evaluate/list' }]
    },
    {
      name: '佣金管理',
      url: [{ name: '分销佣金', url: '/shop/commission/distribution' }]
    },
    {
      name: '售后管理',
      url: [{ name: '售后管理', url: '/shop/AfterSale' }]
    }
  ],

  '/distribution': [
    {
      name: '分销商管理',
      url: [
        { name: '分销商审核', url: '/distribution/examine' },
        { name: '分销商列表', url: '/distribution/list' },
        { name: '分销设置', url: '/distribution/set' }
      ]
    }
  ],

  '/finance': [
    {
      name: '财务管理',
      url: [
        { name: '财务列表', url: '/finance/list' },
        { name: '提现管理', url: '/finance/withdraw' },
        { name: '储值管理', url: '/finance/stored' },
        { name: '历史提现', url: '/finance/withdraw-history' }
      ]
    }
  ],

  '/user': [
    {
      name: '用户管理',
      url: [
        { name: '用户列表', url: '/user/list' },
        { name: '操作日志', url: '/user/log' }
      ]
    }
  ],

  '/account': [
    {
      name: '账号设置',
      url: [
        { name: '管理员管理', url: '/account/admin' },
        { name: '角色管理', url: '/account/role' },
        { name: '菜单管理', url: '/account/menu' },
        { name: '代理商管理', url: '/account/franchisee' },
        { name: '第三方管理', url: '/account/third' }
      ]
    }
  ],

  '/sys': [
    {
      name: '版本管理',
      url: [
        { name: '系统升级', url: '/sys/upgrade' },
        { name: '上传微信审核', url: '/sys/examine' },
        { name: '版本管理', url: '/sys/version' },
        { name: 'APP版本管理', url: '/sys/app-version' }
      ]
    },
    {
      name: '系统设置',
      url: [
        { name: '小程序设置', url: '/sys/wechat' },
        { name: '公众号设置', url: '/sys/web' },
        { name: 'APP设置', url: '/sys/app' },
        { name: '隐私协议', url: '/sys/info' },
        { name: '支付配置', url: '/sys/payment' },
        { name: '上传配置', url: '/sys/upload' },
        { name: '交易设置', url: '/sys/transaction' },
        { name: '万能通知', url: '/sys/notice' },
        { name: '短信通知', url: '/sys/message' },
        { name: '备案信息', url: '/sys/information' }
      ]
    },
    {
      name: '其他设置',
      url: [
        { name: '打印机设置', url: '/sys/print' },
        { name: '车费设置', url: '/sys/car_fee' },
        { name: '城市设置', url: '/sys/city' },
        { name: '出行设置', url: '/sys/travel' },
        { name: '其他设置', url: '/sys/other' }
      ]
    }
  ],

  '/log': [
    {
      name: '日志管理',
      url: [
        { name: '操作日志', url: '/log/operation' }
      ]
    }
  ]
}

// 获取子菜单数据的工具函数
export function getSubmenuByPath(path) {
  return submenuMap[path] || []
}

// 检查菜单是否有子菜单的工具函数
export function hasSubmenu(path) {
  const submenu = submenuMap[path]
  return submenu && submenu.length > 0
}

// 获取菜单的默认路由
export function getDefaultRoute(path) {
  const submenu = submenuMap[path]
  if (submenu && submenu.length > 0 && submenu[0].url && submenu[0].url.length > 0) {
    return submenu[0].url[0].url
  }
  return path
}

// 根据当前路由获取对应的主菜单路径
export function getMainMenuByRoute(currentRoute) {
  for (const menu of mainMenuList) {
    if (currentRoute.startsWith(menu.path)) {
      return menu.path
    }
  }
  return '/service' // 默认返回服务项目
}
