<template>
  <div class="technician-credit-log">
    <!-- 顶部导航 -->
    <TopNav title="师傅信誉分变更日志" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="师傅姓名" prop="coachName">
                <el-input
                  size="default"
                  v-model="searchForm.coachName"
                  placeholder="请输入师傅姓名"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="变更类型" prop="creditType">
                <el-select
                  size="default"
                  v-model="searchForm.creditType"
                  placeholder="请选择变更类型"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="全部" value="" />
                  <el-option label="增加" :value="1" />
                  <el-option label="扣减" :value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="变更原因" prop="changeReason">
                <el-select
                  size="default"
                  v-model="searchForm.changeReason"
                  placeholder="请选择变更原因"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="全部" value="" />
                  <el-option label="订单完成" :value="1" />
                  <el-option label="手动调整" :value="2" />
                  <el-option label="投诉处理" :value="3" />
                </el-select>
              </el-form-item>

              <el-form-item label="操作人类型" prop="operatorType">
                <el-select
                  size="default"
                  v-model="searchForm.operatorType"
                  placeholder="请选择操作人类型"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="全部" value="" />
                  <el-option label="系统自动" :value="1" />
                  <el-option label="管理员" :value="3" />
                </el-select>
              </el-form-item>

              <el-form-item label="变更时间" prop="timeRange">
                <el-date-picker
                  size="default"
                  v-model="searchForm.timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 350px"
                />
              </el-form-item>

              <el-form-item label="选择城市" prop="cityId">
                <el-cascader size="default" v-model="searchForm.cityId" :options="cityOptions" :props="cascaderProps"
                  placeholder="请选择城市" clearable style="width: 200px" @change="handleCityChange" />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="success"
                  icon="Download"
                  @click="handleExport"
                  :loading="exportLoading"
                >
                  导出
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          
          <el-table-column prop="coachName" label="师傅姓名" width="120" />
          
          <el-table-column prop="creditType" label="变更类型" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.creditType === 1 ? 'success' : 'danger'" size="small">
                {{ scope.row.creditType === 1 ? '增加' : '扣减' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="points" label="变更分数" width="100" align="center">
            <template #default="scope">
              <span :class="scope.row.creditType === 1 ? 'text-success' : 'text-danger'">
                {{ scope.row.creditType === 1 ? '+' : '-' }}{{ scope.row.points }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="beforePoints" label="变更前分数" width="120" align="center" />
          
          <el-table-column prop="afterPoints" label="变更后分数" width="120" align="center" />

          <el-table-column prop="changeReason" label="变更原因" width="120" align="center">
            <template #default="scope">
              <el-tag size="small" :type="getReasonTagType(scope.row.changeReason)">
                {{ getReasonText(scope.row.changeReason) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="orderId" label="关联订单" width="120" align="center">
            <template #default="scope">
              <span v-if="scope.row.orderId">{{ scope.row.orderId }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="complaintId" label="投诉ID" width="100" align="center">
            <template #default="scope">
              <span v-if="scope.row.complaintId">{{ scope.row.complaintId }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="operatorType" label="操作人类型" width="120" align="center">
            <template #default="scope">
              <el-tag size="small" :type="scope.row.operatorType === 1 ? 'info' : 'warning'">
                {{ scope.row.operatorType === 1 ? '系统自动' : '管理员' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="变更时间" min-width="160">
            <template #default="scope">
              <div class="time-column">
                <p>{{ formatDate(scope.row.createTime) }}</p>
                <p>{{ formatTime(scope.row.createTime) }}</p>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true, // 返回完整路径，便于显示省市区
  checkStrictly: false, // 只能选择叶子节点（区县级别）
  expandTrigger: 'hover' // 鼠标悬停展开
}

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  coachName: '',
  creditType: '',
  changeReason: '',
  operatorType: '',
  timeRange: [],
  cityId: []
})

// 获取列表数据
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加搜索条件
    if (searchForm.coachName) params.coachName = searchForm.coachName
    if (searchForm.creditType !== '') params.creditType = searchForm.creditType
    if (searchForm.changeReason !== '') params.changeReason = searchForm.changeReason
    if (searchForm.operatorType !== '') params.operatorType = searchForm.operatorType
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      params.cityId = searchForm.cityId.join(',')
      console.log('🏙️ 师傅信誉分变更日志查询包含城市参数:', params.cityId)
    }
    
    // 处理时间范围
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      params.startTime = searchForm.timeRange[0]
      params.endTime = searchForm.timeRange[1]
    }

    console.log('📊 师傅信誉分变更日志查询参数:', params)

    // 调用API
    const result = await api.technician.getCreditPage(params)
    console.log('📊 师傅信誉分变更日志数据:', result)

    if (result.code === 200 || result.code === '200') {
      const data = result.data
      tableData.value = data.list || []
      total.value = data.totalCount || 0

      console.log('✅ 师傅信誉分变更日志加载成功:', {
        list: tableData.value.length,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取师傅信誉分变更日志失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  getTableDataList(1)
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  searchForm.coachName = ''
  searchForm.creditType = ''
  searchForm.changeReason = ''
  searchForm.operatorType = ''
  searchForm.timeRange = []
  searchForm.cityId = []
  getTableDataList(1)
}

// 城市选择处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)

  if (value && value.length > 0) {
    // 取最后一级的城市ID（区县级别）
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

/**
 * 获取城市数据（用于级联选择器）
 */
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 导出
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出师傅信誉分变更日志...')

    // 构建导出参数，过滤空值
    const params = new URLSearchParams()
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] !== '' && searchForm[key] !== null && searchForm[key] !== undefined && key !== 'pageNum' && key !== 'pageSize' && key !== 'timeRange') {
        params.append(key, searchForm[key])
      }
    })

    // 处理时间范围
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      params.append('startTime', searchForm.timeRange[0])
      params.append('endTime', searchForm.timeRange[1])
    }

    // 构建下载URL
    const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3004'}/api/admin/coach/getCreditPage/export`

    // 创建隐藏的下载链接
    const link = document.createElement('a')
    const fullUrl = params.toString() ? `${downloadUrl}?${params.toString()}` : downloadUrl
    link.href = fullUrl
    link.download = `师傅信誉分变更日志_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.target = '_blank'

    // 添加token到请求（如果需要认证）
    const token = sessionStorage.getItem('minitk')
    if (token) {
      const separator = fullUrl.includes('?') ? '&' : '?'
      link.href = `${fullUrl}${separator}token=${encodeURIComponent(token)}`
    }

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出开始，请查看浏览器下载')
    console.log('✅ 导出师傅信誉分变更日志成功')

  } catch (error) {
    console.error('❌ 导出师傅信誉分变更日志异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 获取变更原因文本
const getReasonText = (reason) => {
  const reasonMap = {
    1: '订单完成',
    2: '手动调整',
    3: '投诉处理'
  }
  return reasonMap[reason] || '未知'
}

// 获取变更原因标签类型
const getReasonTagType = (reason) => {
  const typeMap = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return typeMap[reason] || 'info'
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 生命周期
onMounted(() => {
  getTableDataList(1)
  getCityData() // 获取城市数据
})
</script>

<style scoped>
/* 页面主样式 */
.technician-credit-log {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 时间列样式 */
.time-column p {
  margin: 0;
  line-height: 1.6;
  font-size: 14px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

/* 文本颜色样式 */
.text-success {
  color: #67c23a;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c;
  font-weight: 500;
}

.text-muted {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }
}
</style>